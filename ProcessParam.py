import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    raw_param_df = pd.read_excel('ADS_V_CF_PDS_RESULT.xlsx')
    raw_param_df['STEP_ID'] = raw_param_df['STEP_ID'].astype('string')
    raw_param_df = raw_param_df[raw_param_df['STEP_ID'] == '206000'].reset_index(drop=True)
    param_mapping_df = pd.read_excel('制程参数名称.xlsx')

# 定义键列和类型
key_columns = {
    'PRODUCT_ID': 'string',
    'STEP_ID': 'string',
    'GLASS_ID': 'string',
}

# 同一 PRODUCT_ID/STEP_ID/GLASS_ID/PARAM_NAME 只保留GLASS_START_TIME最新的一笔
def deduplicate_raw_data(df):
    """去重：保留每个组合的最新记录"""
    return df.sort_values('GLASS_START_TIME').drop_duplicates(
        subset=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME'],
        keep='last'
    )

# 根据param_mapping_df中的PARAM_NAME/SECONDARY_NAME重命名raw_param_df中的PARAM_NAME
def rename_param_names(df, mapping_df):
    """重命名参数名称"""
    result_df = df.copy()

    # 创建重命名映射
    rename_mapping = {}
    for _, row in mapping_df.iterrows():
        param_name = row['PARAM_NAME']
        secondary_name = row['SECONDARY_NAME']

        # 只重命名存在的列，且有有效的secondary_name
        if (pd.notna(param_name) and pd.notna(secondary_name) and
                param_name in result_df.columns):
            rename_mapping[param_name] = secondary_name

    # 执行重命名
    result_df = result_df.rename(columns=rename_mapping)
    return result_df

# PRODUCT_ID/STEP_ID/GLASS_ID作为键，转置PARAM_NAME栏位，值为PARAM_VALUE(如果PARAM_VALUE为空则取STR_VALUE)
def pivot_data(df):
    """转置数据：以PRODUCT_ID/STEP_ID/GLASS_ID为键"""
    # 处理值：PARAM_VALUE为空时使用STR_VALUE
    df = df.copy()
    df['VALUE'] = df['PARAM_VALUE'].fillna(df['STR_VALUE'])

    # 转置
    pivoted = df.pivot(
        index=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID'],
        columns='PARAM_NAME',
        values='VALUE',
    ).reset_index()

    # 展平列名
    pivoted.columns.name = None
    return pivoted

def apply_operations(df, mapping_df, deduped_raw_df):
    """根据映射表进行数据操作，使用去重后的原始数据"""
    result_df = df.copy()

    # 按操作类型分组，按正确顺序执行
    operations_by_type = {
        'get_time': [],
        'map': [],
        'group': [],
        'mean': []
    }

    # 收集所有操作
    for _, row in mapping_df.iterrows():
        operation = row['OPERATION']
        if pd.notna(operation) and operation in operations_by_type:
            operations_by_type[operation].append(row)

    # 存储分组信息
    groups = {}

    # 1. 先执行 get_time 操作
    for row in operations_by_type['get_time']:
        param_name = row['PARAM_NAME']
        secondary_name = row['SECONDARY_NAME']
        operation_params = row['OPERATION_PARAMETERS']

        if pd.notna(operation_params):
            # 获取指定参数的GLASS_START_TIME
            time_data = deduped_raw_df[
                deduped_raw_df['PARAM_NAME'] == operation_params
                ].set_index(['PRODUCT_ID', 'STEP_ID', 'GLASS_ID'])['GLASS_START_TIME']

            # 将时间数据映射到结果DataFrame
            result_df[secondary_name] = result_df.set_index(
                ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID']
            ).index.map(time_data.to_dict())

            # 重置索引
            result_df = result_df.reset_index(drop=True)

    # 2. 执行 map 操作
    for row in operations_by_type['map']:
        param_name = row['PARAM_NAME']
        secondary_name = row['SECONDARY_NAME']
        operation_params = row['OPERATION_PARAMETERS']

        if pd.notna(operation_params) and param_name in result_df.columns:
            try:
                import ast
                map_dict = ast.literal_eval(operation_params)
                print(map_dict)

                # 创建新列
                result_df[secondary_name] = result_df[param_name].map(map_dict)

                # 只有当新列名不同于原列名时才删除原列
                if param_name != secondary_name:
                    result_df = result_df.drop(columns=[param_name])

            except Exception as e:
                print(f"Warning: Could not parse mapping dictionary {operation_params}: {e}")

    # 3. 收集 group 定义
    for row in operations_by_type['group']:
        param_name = row['PARAM_NAME']  # 这应该是要分组的列名
        secondary_name = row['SECONDARY_NAME']  # 这可能是列的别名
        operation_params = row['OPERATION_PARAMETERS']  # 这是组名

        if pd.notna(operation_params):
            group_name = operation_params

            # 确定要添加到组中的列名
            # 优先使用secondary_name，如果不存在则使用param_name
            column_to_group = None
            if secondary_name in result_df.columns:
                column_to_group = secondary_name
            elif param_name in result_df.columns:
                column_to_group = param_name

            if column_to_group:
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(column_to_group)
            else:
                print(f"Warning: Neither {secondary_name} nor {param_name} found in columns for group {group_name}")

    # 4. 执行 mean 操作
    for row in operations_by_type['mean']:
        secondary_name = row['SECONDARY_NAME']
        operation_params = row['OPERATION_PARAMETERS']  # 组名

        if pd.notna(operation_params) and operation_params in groups:
            group_columns = groups[operation_params]
            available_columns = [col for col in group_columns if col in result_df.columns]

            if available_columns:
                # 转换为数值类型
                numeric_data = result_df[available_columns].apply(pd.to_numeric, errors='coerce')

                # 对每一行计算非零值的均值
                def calculate_non_zero_mean(row):
                    # 获取非零且非NaN的值
                    non_zero_values = row[(row != 0) & (row.notna())]

                    if len(non_zero_values) > 0:
                        return non_zero_values.mean()
                    else:
                        # 如果全为0或全为NaN，返回pd.NA
                        return pd.NA

                result_df[secondary_name] = numeric_data.apply(calculate_non_zero_mean, axis=1)
            else:
                print(f"Warning: No available columns found for group '{operation_params}'")
        else:
            print(f"Warning: Group '{operation_params}' not found for mean operation '{secondary_name}'")

    return result_df

def convert_dtypes(df, mapping_df):
    """显式转换数据类型"""
    result_df = df.copy()

    for _, row in mapping_df.iterrows():
        secondary_name = row['SECONDARY_NAME']
        dtype = row['DTYPE']

        if secondary_name in result_df.columns and pd.notna(dtype):
            try:
                if dtype.lower() == 'int':
                    result_df[secondary_name] = pd.to_numeric(result_df[secondary_name], errors='coerce').astype('Int64')
                elif dtype.lower() == 'float':
                    result_df[secondary_name] = pd.to_numeric(result_df[secondary_name], errors='coerce').astype('Float64')
                elif dtype.lower() == 'string':
                    result_df[secondary_name] = result_df[secondary_name].astype('string')
                elif dtype.lower() == 'datetime':
                    result_df[secondary_name] = pd.to_datetime(result_df[secondary_name], errors='coerce')
                print(f"Converted {secondary_name} to {dtype}")
            except Exception as e:
                print(f"Warning: Could not convert {secondary_name} to {dtype}: {e}")

    for col, dtype in key_columns.items():
        if col in result_df.columns:
            try:
                if dtype.lower() == 'int':
                    result_df[col] = pd.to_numeric(result_df[col], errors='coerce').astype('Int64')
                elif dtype.lower() == 'float':
                    result_df[col] = pd.to_numeric(result_df[col], errors='coerce').astype('Float64')
                elif dtype.lower() == 'string':
                    result_df[col] = result_df[col].astype('string')
                elif dtype.lower() == 'datetime':
                    result_df[col] = pd.to_datetime(result_df[col], errors='coerce')
            except Exception as e:
                print(f"Warning: Could not convert {col} to {dtype}: {e}")

    return result_df

def reorder_and_sort(df, mapping_df):
    """根据SECONDARY_NAME顺序重排列，并按涂布时间升序排序"""
    # 获取所有SECONDARY_NAME的顺序
    secondary_names = mapping_df['SECONDARY_NAME'].dropna().tolist()

    # 构建最终列顺序：主键列 + 按mapping_df顺序的SECONDARY_NAME列
    final_columns = list(key_columns.keys())
    for col in secondary_names:
        if col not in final_columns:  # 移除了 col in df.columns 的检查
            final_columns.append(col)

    # 确保所有需要的列都存在，不存在的列用NaN填充
    result_df = df.copy()
    for col in final_columns:
        if col not in result_df.columns:
            result_df[col] = pd.NA  # 或者使用 pd.NA

    # 重排列顺序
    result_df = result_df[final_columns]

    # 按涂布时间升序排序
    if '涂布时间' in result_df.columns:
        result_df = result_df.sort_values('涂布时间', ascending=True)
    else:
        print("Warning: '涂布时间' column not found for sorting")

    return result_df

# 主处理流程
def process_data():
    """主数据处理流程"""
    # 步骤1：去重
    deduped_df = deduplicate_raw_data(raw_param_df)

    # 步骤2：转置（使用原始PARAM_NAME）
    pivoted_df = pivot_data(deduped_df)

    # 步骤3：数据操作（基于原始PARAM_NAME）
    operated_df = apply_operations(pivoted_df, param_mapping_df, deduped_df)

    # 步骤4：重命名列（最后进行重命名）
    renamed_df = rename_param_names(operated_df, param_mapping_df)

    # 步骤5：重排列顺序并排序
    reordered_df = reorder_and_sort(renamed_df, param_mapping_df)

    # 步骤6：类型转换
    final_df = convert_dtypes(reordered_df, param_mapping_df)

    return final_df

# 执行处理
processed_df = process_data()
if processed_df is not None:
    print("数据处理完成")
    print(f"最终数据形状: {processed_df.shape}")
    print(f"列名: {list(processed_df.columns)}")

    if platform == 'ide':
        processed_df.to_excel('processed_result.xlsx', index=False)
        processed_df.dtypes.to_excel('dtypes.xlsx')
