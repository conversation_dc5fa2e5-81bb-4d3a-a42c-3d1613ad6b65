import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    meas_items_df = pd.read_csv('量测项目名称.csv')
    meas_data_df = pd.read_csv('ADS_V_CF_RESULT.csv')
    step_id = '202000'

# meas_items_df格式：
# MAIN_STEP	MEAS_NAME	OPERATION	STEP_ID	STEP_GROUP	PARAM_NAME
# 202000	CD1	mean	202000/202400/202405	TTP	CD1
# 202000	CD_Range	range	202000/202400/202405	TTP	CD1
# 202000	THK_S	start	202301/202306	PRF	THK_BM/THK_S

# 根据MAIN_STEP == step_id确定要处理的量测项目
# 逐个处理MEAS_NAME，每个MEAS_NAME生成一个DataFrame
# 键为PRODUCT_ID/STEP_ID/GLASS_ID/GLASS_END_TIME/PARAM_NAME，值列名为MEAS_NAME定义的名称
# 值的计算方式为OPERATION。共有以下几种聚合方式：
# 1. mean：对PARAM_VALUE取平均值
# 2. range：对PARAM_VALUE取最大值减最小值
# 3. max：对PARAM_VALUE取最大值
# 4. min：对PARAM_VALUE取最小值
# 5. start：筛选出Y为负的行，对PARAM_VALUE取最大值减最小值
# 6. end：筛选出Y为正的行，对PARAM_VALUE取最大值减最小值
# 该MEAS_NAME允许处理的STEP_ID和PARAM_NAME由斜杠分割
